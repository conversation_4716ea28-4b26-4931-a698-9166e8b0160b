<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-19T13:51:05.655325" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-619_ATM_DETAIL_Verify_Sort_Selection_Functionality_Sort_with_Default_Rows_per_Page.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.441296" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.441296" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.440291" elapsed="0.001005"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.442301" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.442301" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.442301" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.443300" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.442301" elapsed="0.000999"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.442301" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.443300" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:11.443300" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.443300" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.444297" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.444297" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.443300" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.444297" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.444297" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.444297" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.445286" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:11.444297" elapsed="0.000989"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.444297" elapsed="0.000989"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.445286" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.445286" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.445286" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:11.439292" elapsed="0.007578"/>
</kw>
<test id="s1-t1" name="Verify Sort Selection Functionality: Sort with Default Rows per Page" line="44">
<kw name="Verify Sort Selection Functionality: Sort with Default Rows per Page">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.447838" level="INFO">Set test documentation to:
Verify Sort Selection Functionality: Sort with Default Rows per Page</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-19T13:51:11.447838" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.448855" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:51:11.447838" elapsed="0.001017"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:51:11.448855" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:51:11.448855" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.450862" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-19T13:51:11.450862" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-19T13:51:11.450862" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:51:11.450862" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:11.450862" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:51:11.450862" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.451855" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:11.451855" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:11.450862" elapsed="0.000993"/>
</branch>
<status status="PASS" start="2025-06-19T13:51:11.450862" elapsed="0.000993"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-19T13:51:11.513821" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-19T13:51:11.513821" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-19T13:51:11.451855" elapsed="0.061966"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.515815" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:51:11.514820" elapsed="0.000995"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.515815" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:11.515815" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:11.515815" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:51:11.515815" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-19T13:51:11.516815" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-19T13:51:11.515815" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:51:11.516815" elapsed="0.000518"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-19T13:51:11.717955" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-19T13:51:12.587526" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-19T13:51:12.587526" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-19T13:51:11.517333" elapsed="1.070193"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.587526" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:12.587526" elapsed="0.001026"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:12.587526" elapsed="0.001026"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-19T13:51:11.514820" elapsed="1.073732"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:12.588552" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-19T13:51:12.588552" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T13:51:12.588552" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T13:51:12.588552" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T13:51:12.589575" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T13:51:12.589575" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:51:12.589575" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:51:12.589575" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.590578" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:12.590578" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-19T13:51:12.591578" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000022C530489B0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-19T13:51:12.591578" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-19T13:51:12.591578" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:12.591578" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:51:12.591578" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.592576" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000022C5304ADB0&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:51:12.592576" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.592576" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:12.592576" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.593575" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:12.592576" elapsed="0.000999"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.593575" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:51:12.593575" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-19T13:51:12.594573" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-19T13:51:12.593575" elapsed="0.000998"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-19T13:51:12.594702" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:12.594975" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T13:51:12.594975" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-19T13:51:12.594975" elapsed="4.908073"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:12.592576" elapsed="4.911587"/>
</branch>
<status status="PASS" start="2025-06-19T13:51:12.591578" elapsed="4.912585"/>
</if>
<status status="PASS" start="2025-06-19T13:51:12.590578" elapsed="4.913585"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.505195" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.505924" elapsed="0.000519"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.508997" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.510032" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:51:17.506532" elapsed="0.003500"/>
</branch>
<status status="NOT RUN" start="2025-06-19T13:51:17.506532" elapsed="0.003500"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.510032" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.511036" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.512032" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.513026" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.514490" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.514490" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.515681" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.515681" elapsed="0.001096"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.516777" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.517742" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.517742" elapsed="0.001025"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.518767" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T13:51:17.519820" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:51:17.504163" elapsed="0.015657"/>
</branch>
<status status="PASS" start="2025-06-19T13:51:12.589575" elapsed="4.930245"/>
</if>
<status status="PASS" start="2025-06-19T13:51:11.514820" elapsed="6.005000"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-19T13:51:17.522038" elapsed="0.158842"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:17.686104" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-19T13:51:17.686104" elapsed="3.604361"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:17.681882" elapsed="3.608583"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:51:31.292019" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:51:21.291470" elapsed="10.000549"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:31.292582" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:51:31.309991" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:31.292582" elapsed="0.017409"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:51:31.309991" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:51:41.311486" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:51:31.310992" elapsed="10.000494"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:41.311486" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:51:41.345349" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:41.311486" elapsed="0.033863"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:51:41.345349" elapsed="0.001007"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:51:46.346837" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:51:41.346356" elapsed="5.000481"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:46.347858" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:51:46.429313" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:46.347858" elapsed="0.081455"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:51:46.430307" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-19T13:51:17.521003" elapsed="28.910309"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:46.573616" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:51:46.431312" elapsed="0.142304"/>
</kw>
<msg time="2025-06-19T13:51:46.575118" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:51:46.431312" elapsed="0.143806"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:46.575711" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:46.575711" elapsed="0.213549"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:51:46.790259" elapsed="0.041210"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:46.831469" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:46.831469" elapsed="0.298175"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:47.130641" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:47.130641" elapsed="0.623357"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:51:49.756023" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:51:47.755526" elapsed="2.000497"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:49.824535" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:51:49.758374" elapsed="0.066308"/>
</kw>
<msg time="2025-06-19T13:51:49.825290" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:51:49.757639" elapsed="0.067651"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:46.575118" elapsed="3.250172"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:49.828246" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:49.826254" elapsed="0.159088"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:51:49.986321" elapsed="0.048024"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:50.034345" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:50.034345" elapsed="0.214995"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:50.250378" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:50.250378" elapsed="3.915716"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:51:56.166594" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:51:54.166094" elapsed="2.000500"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:56.310092" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-52.png"&gt;&lt;img src="selenium-screenshot-52.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-19T13:51:56.311092" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-19T13:51:56.166594" elapsed="0.147497">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-19T13:51:56.314091" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:51:56.166594" elapsed="0.147497"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:49.825290" elapsed="6.488801"/>
</iter>
<status status="PASS" start="2025-06-19T13:51:46.575118" elapsed="9.738973"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:51:56.314091" elapsed="0.000998"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:56.469004" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-53.png"&gt;&lt;img src="selenium-screenshot-53.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-19T13:51:56.315089" elapsed="0.153915"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-19T13:51:11.449853" elapsed="45.019151"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:51:11.449853" elapsed="45.019151"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-19T13:51:11.447838" elapsed="45.021166"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:51:56.469004" elapsed="0.061440"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-19T13:51:56.530444" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:51:56.530444" elapsed="9.154322"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:05.684766" elapsed="0.002517"/>
</kw>
<status status="PASS" start="2025-06-19T13:51:56.469004" elapsed="9.218279"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:05.688313" elapsed="0.077565"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:05.766898" elapsed="0.029862"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:05.895707" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:05.796760" elapsed="0.098947"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:05.895707" elapsed="0.034659"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.060169" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:05.930366" elapsed="0.130799"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.208091" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.061165" elapsed="0.148243"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.313499" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.209408" elapsed="0.105093"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.427220" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.314501" elapsed="0.112719"/>
</kw>
<msg time="2025-06-19T13:52:06.427220" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:52:06.314501" elapsed="0.112719"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.504765" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.428219" elapsed="0.076546"/>
</kw>
<msg time="2025-06-19T13:52:06.505303" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:52:06.428219" elapsed="0.077084"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.598519" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.505924" elapsed="0.092595"/>
</kw>
<msg time="2025-06-19T13:52:06.598519" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:52:06.505391" elapsed="0.093128"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:06.599520" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:52:05.687283" elapsed="0.912237"/>
</kw>
<kw name="Then The user chooses and clicks Sorting Criteria" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:06.600696" elapsed="0.064816"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.665512" level="INFO">Clicking element 'xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), 'ATM Branch')]'.</msg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.665512" elapsed="0.217983"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Sort Criteria Element Has Been Clicked: ${SORT_CRITERIA}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:06.884502" elapsed="0.001115"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:06.888654" level="INFO">${Global_Sort_Criteria_element} = xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), 'ATM Branch')]</msg>
<arg>${Global_Sort_Criteria_element}</arg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:52:06.885617" elapsed="0.003037"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:06.895159" level="INFO">${Global_Sort_Criteria_elements} = xpath=//*[@id="root"]/div/table/thead/tr/th/span</msg>
<arg>${Global_Sort_Criteria_elements}</arg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:52:06.889646" elapsed="0.006536"/>
</kw>
<arg>${SORT_CRITERIA}</arg>
<status status="PASS" start="2025-06-19T13:52:06.599520" elapsed="0.296662"/>
</kw>
<kw name="Then The user verifies that the data is sorted by The Sort Criteria in Ascending order" owner="ATMDetails">
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.931309" level="INFO">${all_headers} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.245")&gt;, &lt;selenium.webdri...</msg>
<var>${all_headers}</var>
<arg>${Global_Sort_Criteria_elements}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.899291" elapsed="0.033019"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:06.962190" level="INFO">${header_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.247")&gt;</msg>
<var>${header_element}</var>
<arg>${Global_Sort_Criteria_element}</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.933313" elapsed="0.028877"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.004535" level="INFO">${header_element_text} = ATM Branch▼</msg>
<var>${header_element_text}</var>
<arg>${header_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:06.963195" elapsed="0.041340"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.006379" level="INFO">${cleaned_header_element_text} = ATMBranch</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.004535" elapsed="0.001844"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.011378" level="INFO">${cleaned_h_element_text} = ATMBranch</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.007380" elapsed="0.003998"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.012373" elapsed="0.001011"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.015452" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:52:07.014463" elapsed="0.000989"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.017455" level="INFO">Length is 12.</msg>
<msg time="2025-06-19T13:52:07.017455" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>${all_headers}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-19T13:52:07.016455" elapsed="0.001000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.019446" elapsed="0.001001"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.021445" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.245")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.021445" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.056095" level="INFO">${header_text} = ATM Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.023081" elapsed="0.033014"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.058231" level="INFO">${cleaned_header_text} = ATMNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.057201" elapsed="0.001030"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.063226" level="INFO">${cleaned_h_text} = ATMNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.059230" elapsed="0.003996"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.063226" elapsed="0.002551"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.067781" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.068783" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.068783" elapsed="0.000997"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:52:07.066785" elapsed="0.002995"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.065777" elapsed="0.004003"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-19T13:52:07.019446" elapsed="0.050334"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.070778" elapsed="0.002000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.072778" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.246")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.072778" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.107747" level="INFO">${header_text} = Serial Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.074361" elapsed="0.034382"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.109749" level="INFO">${cleaned_header_text} = SerialNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.108743" elapsed="0.001998"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.114740" level="INFO">${cleaned_h_text} = SerialNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.110741" elapsed="0.003999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.116890" elapsed="0.000994"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.117884" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.119197" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.119197" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:52:07.117884" elapsed="0.001313"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.117884" elapsed="0.001313"/>
</if>
<var name="${i}">1</var>
<status status="PASS" start="2025-06-19T13:52:07.070778" elapsed="0.048419"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.119197" elapsed="0.001027"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.121224" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.247")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.121224" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.148927" level="INFO">${header_text} = ATM Branch▼</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.121224" elapsed="0.027703"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.148927" level="INFO">${cleaned_header_text} = ATMBranch</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.148927" elapsed="0.000998"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.150928" level="INFO">${cleaned_h_text} = ATMBranch</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.149925" elapsed="0.001003"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.150928" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.152456" level="INFO">${index} = 2</msg>
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:52:07.151928" elapsed="0.000528"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.152456" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.153476" level="INFO">Exiting for loop altogether.</msg>
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="PASS" start="2025-06-19T13:52:07.153476" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:52:07.151928" elapsed="0.001548"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.151928" elapsed="0.001548"/>
</if>
<var name="${i}">2</var>
<status status="PASS" start="2025-06-19T13:52:07.119197" elapsed="0.034279"/>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="PASS" start="2025-06-19T13:52:07.018450" elapsed="0.135026"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Header was not found</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:52:07.153476" elapsed="0.000000"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.175839" level="INFO">${elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.257")&gt;, &lt;selenium.webdri...</msg>
<var>${elements}</var>
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.153476" elapsed="0.022363"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.175839" level="INFO">Length is 10.</msg>
<msg time="2025-06-19T13:52:07.175839" level="INFO">${num_elements} = 10</msg>
<var>${num_elements}</var>
<arg>${elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-19T13:52:07.175839" elapsed="0.000000"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.176833" level="INFO">${texts} = []</msg>
<var>${texts}</var>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-19T13:52:07.176833" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.176833" elapsed="0.000998"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.178830" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.257")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.177831" elapsed="0.002006"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.209570" level="INFO">${text} = A1 Hyper Chickens</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.179837" elapsed="0.029733"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.210569" elapsed="0.000998"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.212568" elapsed="0.000000"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-19T13:52:07.176833" elapsed="0.035735"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.212568" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.213574" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.258")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.213574" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.240832" level="INFO">${text} = ABSA ALBERTON</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.213574" elapsed="0.027258"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.241858" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.242863" elapsed="0.000552"/>
</kw>
<var name="${i}">1</var>
<status status="PASS" start="2025-06-19T13:52:07.212568" elapsed="0.030847"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.245014" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.246049" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.259")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.245014" elapsed="0.001035"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.271955" level="INFO">${text} = ABSA ALBERTON</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.246049" elapsed="0.025906"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.271955" elapsed="0.001461"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.274496" elapsed="0.000000"/>
</kw>
<var name="${i}">2</var>
<status status="PASS" start="2025-06-19T13:52:07.244493" elapsed="0.030003"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.275929" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.277159" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.260")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.275929" elapsed="0.001230"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.302959" level="INFO">${text} = ABSA ALBERTON</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.277159" elapsed="0.025800"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.304545" elapsed="0.001414"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.305959" elapsed="0.000000"/>
</kw>
<var name="${i}">3</var>
<status status="PASS" start="2025-06-19T13:52:07.274496" elapsed="0.031463"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.306984" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.308982" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.261")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.307984" elapsed="0.000998"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.336635" level="INFO">${text} = ABSA BUILDNG</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.309978" elapsed="0.026657"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.337643" elapsed="0.002001"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.340637" elapsed="0.001002"/>
</kw>
<var name="${i}">4</var>
<status status="PASS" start="2025-06-19T13:52:07.305959" elapsed="0.035680"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.342639" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.344637" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.262")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.343638" elapsed="0.000999"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.371508" level="INFO">${text} = ABSA Botshabelo</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.344637" elapsed="0.026871"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.371508" elapsed="0.002004"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.373512" elapsed="0.000000"/>
</kw>
<var name="${i}">5</var>
<status status="PASS" start="2025-06-19T13:52:07.342639" elapsed="0.030873"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.374514" elapsed="0.000523"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.375037" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.263")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.375037" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.400994" level="INFO">${text} = ABSA Carnarvon</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.375037" elapsed="0.025957"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.401993" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.402993" elapsed="0.000000"/>
</kw>
<var name="${i}">6</var>
<status status="PASS" start="2025-06-19T13:52:07.373512" elapsed="0.029481"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.403569" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.405612" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.264")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.404594" elapsed="0.002041"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.430287" level="INFO">${text} = ABSA Carnarvon</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.406635" elapsed="0.023652"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.431287" elapsed="0.000997"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.432284" elapsed="0.000000"/>
</kw>
<var name="${i}">7</var>
<status status="PASS" start="2025-06-19T13:52:07.403569" elapsed="0.028715"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.433281" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.433281" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.265")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.433281" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.461136" level="INFO">${text} = ABSA De Doorns</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.434284" elapsed="0.026852"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.462135" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.463129" elapsed="0.000000"/>
</kw>
<var name="${i}">8</var>
<status status="PASS" start="2025-06-19T13:52:07.432284" elapsed="0.030845"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.463129" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.463129" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.266")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.463129" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.481271" level="INFO">${text} = ABSA Douglas</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.463129" elapsed="0.018142"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.482735" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.482735" elapsed="0.000000"/>
</kw>
<var name="${i}">9</var>
<status status="PASS" start="2025-06-19T13:52:07.463129" elapsed="0.020632"/>
</iter>
<var>${i}</var>
<value>${num_elements}</value>
<status status="PASS" start="2025-06-19T13:52:07.176833" elapsed="0.306928"/>
</for>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.483761" level="INFO">${sorted_texts} = ['A1 Hyper Chickens', 'ABSA ALBERTON', 'ABSA ALBERTON', 'ABSA ALBERTON', 'ABSA BUILDNG', 'ABSA Botshabelo', 'ABSA Carnarvon', 'ABSA Carnarvon', 'ABSA De Doorns', 'ABSA Douglas']</msg>
<var>${sorted_texts}</var>
<arg>sorted(${texts})</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.483761" elapsed="0.000525"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${texts}</arg>
<arg>${sorted_texts}</arg>
<arg>Fail</arg>
<arg>--------------------------The texts are not in ascending order</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-06-19T13:52:07.484286" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The texts are in ascending order</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.484286" elapsed="0.001065"/>
</kw>
<return>
<value>${index}</value>
<status status="PASS" start="2025-06-19T13:52:07.485351" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-06-19T13:52:06.898256" elapsed="0.587095"/>
</kw>
<kw name="Then The user chooses and clicks Sorting Criteria" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:07.485351" elapsed="0.128185"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.614643" level="INFO">Clicking element 'xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), 'ATM Branch')]'.</msg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.614643" elapsed="0.124865"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Sort Criteria Element Has Been Clicked: ${SORT_CRITERIA}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.739508" elapsed="0.001450"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.742335" level="INFO">${Global_Sort_Criteria_element} = xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), 'ATM Branch')]</msg>
<arg>${Global_Sort_Criteria_element}</arg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${SORT_CRITERIA}')]</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:52:07.740958" elapsed="0.001377"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.742335" level="INFO">${Global_Sort_Criteria_elements} = xpath=//*[@id="root"]/div/table/thead/tr/th/span</msg>
<arg>${Global_Sort_Criteria_elements}</arg>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:52:07.742335" elapsed="0.000000"/>
</kw>
<arg>${SORT_CRITERIA}</arg>
<status status="PASS" start="2025-06-19T13:52:07.485351" elapsed="0.256984"/>
</kw>
<kw name="Then The user verifies that the data is sorted by The Sort Criteria in Descending order" owner="ATMDetails">
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.752934" level="INFO">${all_headers} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.267")&gt;, &lt;selenium.webdri...</msg>
<var>${all_headers}</var>
<arg>${Global_Sort_Criteria_elements}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.743361" elapsed="0.009573"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.768564" level="INFO">${header_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.269")&gt;</msg>
<var>${header_element}</var>
<arg>${Global_Sort_Criteria_element}</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.752934" elapsed="0.015630"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.782050" level="INFO">${header_element_text} = ATM Branch▲</msg>
<var>${header_element_text}</var>
<arg>${header_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.769568" elapsed="0.012482"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.783054" level="INFO">${cleaned_header_element_text} = ATMBranch</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.783054" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.783054" level="INFO">${cleaned_h_element_text} = ATMBranch</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.783054" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.783054" elapsed="0.001526"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.784746" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:52:07.784746" elapsed="0.000000"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.784746" level="INFO">Length is 12.</msg>
<msg time="2025-06-19T13:52:07.784746" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>${all_headers}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-19T13:52:07.784746" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.785601" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.785601" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.267")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.785601" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.797477" level="INFO">${header_text} = ATM Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.785601" elapsed="0.011876"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.797477" level="INFO">${cleaned_header_text} = ATMNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.797477" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.798479" level="INFO">${cleaned_h_text} = ATMNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.797477" elapsed="0.001002"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.798479" elapsed="0.000995"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.799474" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.799474" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.799474" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:52:07.799474" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.799474" elapsed="0.001237"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-19T13:52:07.784746" elapsed="0.015965"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.800711" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.800711" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.268")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.800711" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.811404" level="INFO">${header_text} = Serial Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.801732" elapsed="0.009672"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.812394" level="INFO">${cleaned_header_text} = SerialNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.811404" elapsed="0.000990"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.814392" level="INFO">${cleaned_h_text} = SerialNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.812394" elapsed="0.001998"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.814392" elapsed="0.000519"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.814911" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.814911" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-19T13:52:07.814911" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:52:07.814911" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.814911" elapsed="0.000000"/>
</if>
<var name="${i}">1</var>
<status status="PASS" start="2025-06-19T13:52:07.800711" elapsed="0.015230"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.815941" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.815941" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.269")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.815941" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.830511" level="INFO">${header_text} = ATM Branch▲</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.816931" elapsed="0.013580"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.831510" level="INFO">${cleaned_header_text} = ATMBranch</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:07.831510" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:52:07.833510" level="INFO">${cleaned_h_text} = ATMBranch</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.831510" elapsed="0.002000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.833510" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.835035" level="INFO">${index} = 2</msg>
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:52:07.835035" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.835035" elapsed="0.001025"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.836060" level="INFO">Exiting for loop altogether.</msg>
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="PASS" start="2025-06-19T13:52:07.836060" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:52:07.834512" elapsed="0.001548"/>
</branch>
<status status="PASS" start="2025-06-19T13:52:07.834512" elapsed="0.001548"/>
</if>
<var name="${i}">2</var>
<status status="PASS" start="2025-06-19T13:52:07.815941" elapsed="0.020119"/>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="PASS" start="2025-06-19T13:52:07.784746" elapsed="0.051314"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Header was not found</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:52:07.836060" elapsed="0.001025"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.852751" level="INFO">${elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.281")&gt;, &lt;selenium.webdri...</msg>
<var>${elements}</var>
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.837085" elapsed="0.015666"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.852751" level="INFO">Length is 10.</msg>
<msg time="2025-06-19T13:52:07.852751" level="INFO">${num_elements} = 10</msg>
<var>${num_elements}</var>
<arg>${elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-19T13:52:07.852751" elapsed="0.000000"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-19T13:52:07.853274" level="INFO">${texts} = []</msg>
<var>${texts}</var>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-19T13:52:07.853274" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.853274" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.854359" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.281")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.854289" elapsed="0.000070"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.867256" level="INFO">${text} = Zevenwacht Branch</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.854359" elapsed="0.012897"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.867256" elapsed="0.001003"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.868259" elapsed="0.000000"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-19T13:52:07.853274" elapsed="0.014985"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.868259" elapsed="0.001008"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.869267" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.282")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.869267" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.881726" level="INFO">${text} = Zevenwacht Branch</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.869267" elapsed="0.012459"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.881726" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.882730" elapsed="0.000000"/>
</kw>
<var name="${i}">1</var>
<status status="PASS" start="2025-06-19T13:52:07.868259" elapsed="0.014471"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.882730" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.882730" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.283")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.882730" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.895824" level="INFO">${text} = Zevenwacht Branch</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.882730" elapsed="0.013094"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.895824" elapsed="0.001023"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.896847" elapsed="0.000000"/>
</kw>
<var name="${i}">2</var>
<status status="PASS" start="2025-06-19T13:52:07.882730" elapsed="0.014117"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.896847" elapsed="0.001005"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.928660" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.284")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.897852" elapsed="0.030808"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.937956" level="INFO">${text} = Zeerust</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.928660" elapsed="0.009296"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.937956" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.937956" elapsed="0.001008"/>
</kw>
<var name="${i}">3</var>
<status status="PASS" start="2025-06-19T13:52:07.896847" elapsed="0.042117"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.938964" elapsed="0.000994"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.939958" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.285")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.939958" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.949522" level="INFO">${text} = Zeerust</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.940960" elapsed="0.008562"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.950530" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.951529" elapsed="0.000000"/>
</kw>
<var name="${i}">4</var>
<status status="PASS" start="2025-06-19T13:52:07.938964" elapsed="0.012565"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.951529" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.951529" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.286")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.951529" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.963038" level="INFO">${text} = Zeerust</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.953051" elapsed="0.009987"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.963038" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.964751" elapsed="0.000000"/>
</kw>
<var name="${i}">5</var>
<status status="PASS" start="2025-06-19T13:52:07.951529" elapsed="0.013222"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.964751" elapsed="0.000821"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.966577" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.287")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.966577" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.978131" level="INFO">${text} = Zeerust</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.966577" elapsed="0.011554"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.978131" elapsed="0.000991"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.979122" elapsed="0.000000"/>
</kw>
<var name="${i}">6</var>
<status status="PASS" start="2025-06-19T13:52:07.964751" elapsed="0.014371"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.979122" elapsed="0.001014"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.980136" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.288")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.980136" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:07.989953" level="INFO">${text} = Zastron</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.980136" elapsed="0.009817"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.989953" elapsed="0.001040"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.990993" elapsed="0.001002"/>
</kw>
<var name="${i}">7</var>
<status status="PASS" start="2025-06-19T13:52:07.979122" elapsed="0.012873"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:07.991995" elapsed="0.000997"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:07.992992" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.289")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.992992" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:08.001561" level="INFO">${text} = Zastron</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:07.992992" elapsed="0.008569"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:08.002558" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:08.002558" elapsed="0.000000"/>
</kw>
<var name="${i}">8</var>
<status status="PASS" start="2025-06-19T13:52:07.991995" elapsed="0.010563"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:08.003558" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:52:08.003558" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="2b0707443d2babdd99ebd921a43a1970", element="f.2BA0FADE857C7C9D50590F342E2AE80F.d.DADDA6A160FF43A7F45A9B699768D0AE.e.290")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:08.003558" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:08.016357" level="INFO">${text} = Zastron</msg>
<var>${text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:08.004559" elapsed="0.011798"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:08.017371" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${texts}</arg>
<arg>${text}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-19T13:52:08.017371" elapsed="0.001001"/>
</kw>
<var name="${i}">9</var>
<status status="PASS" start="2025-06-19T13:52:08.003558" elapsed="0.014814"/>
</iter>
<var>${i}</var>
<value>${num_elements}</value>
<status status="PASS" start="2025-06-19T13:52:07.853274" elapsed="0.165098"/>
</for>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:52:08.018372" level="INFO">${sorted_texts} = ['Zevenwacht Branch', 'Zevenwacht Branch', 'Zevenwacht Branch', 'Zeerust', 'Zeerust', 'Zeerust', 'Zeerust', 'Zastron', 'Zastron', 'Zastron']</msg>
<var>${sorted_texts}</var>
<arg>sorted(${texts}, reverse=True)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:52:08.018372" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${texts}</arg>
<arg>${sorted_texts}</arg>
<arg>Fail</arg>
<arg>--------------------------The texts are not in descending order</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-06-19T13:52:08.018372" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The texts are in descending order</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:08.019371" elapsed="0.000000"/>
</kw>
<return>
<value>${index}</value>
<status status="PASS" start="2025-06-19T13:52:08.019371" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-06-19T13:52:07.742335" elapsed="0.277036"/>
</kw>
<arg>Verify Sort Selection Functionality: Sort with Default Rows per Page</arg>
<arg>VMS_UAT</arg>
<arg>ATM Branch</arg>
<status status="PASS" start="2025-06-19T13:51:11.447838" elapsed="56.571533"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:52:08.020372" elapsed="0.023144"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:52:08.043516" elapsed="0.001000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:52:08.045034" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:52:08.044516" elapsed="0.093803"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:52:11.140684" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:52:08.139326" elapsed="3.001358"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-19T13:52:11.141709" elapsed="3.099658"/>
</kw>
<status status="PASS" start="2025-06-19T13:52:08.019371" elapsed="6.221996"/>
</kw>
<doc>Verify Sort Selection Functionality: Sort with Default Rows per Page</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-19T13:51:11.446870" elapsed="62.795498"/>
</test>
<doc>Verify Sort Selection Functionality: Sort with Default Rows per Page</doc>
<status status="PASS" start="2025-06-19T13:51:05.813671" elapsed="68.430694"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">ATM DETAILS</stat>
<stat pass="1" fail="0" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-19T13:51:05.805432" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T13:51:10.234617" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.235620" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.235620" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 258: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.235620" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 298: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.237239" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 343: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.237239" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.238187" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 398: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.239209" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 694: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.240209" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 716: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.241473" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 747: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.241780" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 783: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.242515" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 793: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.242615" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 804: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.243648" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 822: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.243648" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 829: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.244681" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 849: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.245685" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 876: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.245685" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 907: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.245685" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1013: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.248237" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1241: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.248237" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1352: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.277848" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.278851" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.353310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:10.353310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 372: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:51:11.424965" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T13:51:11.436103" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-19T13:51:12.587526" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-19T13:51:12.594975" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T13:51:31.292582" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:51:41.311486" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:51:46.347858" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
