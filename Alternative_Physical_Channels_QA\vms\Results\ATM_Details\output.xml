<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-19T13:31:23.152243" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-617_ATM_DETAIL_Verify_Sort_Selection_Functionality_Sort_with_Changed_Rows_per_Page.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.894737" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.894737" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.894737" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.895734" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.895734" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.897218" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.001484"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.895734" elapsed="0.001484"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.897218" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.897218" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.897218" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.898242" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.897218" elapsed="0.001024"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.897218" elapsed="0.001024"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.898719" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898242" elapsed="0.000477"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.898719" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.898719" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:31:25.893741" elapsed="0.004978"/>
</kw>
<test id="s1-t1" name="Verify Sort Selection Functionality: Sort with Changed Rows per Page" line="41">
<kw name="Verify Sort Selection Functionality: Sort with Changed Rows per Page">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.898719" level="INFO">Set test documentation to:
Verify Sort Selection Functionality: Sort with Changed Rows per Page</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.898719" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:25.903977" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-19T13:31:25.903977" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-19T13:31:25.903977" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:31:25.903977" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:25.903977" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:31:25.903977" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.905004" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:25.905004" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:31:25.905004" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T13:31:25.903977" elapsed="0.001027"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-19T13:31:25.913758" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-19T13:31:25.913758" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-19T13:31:25.905004" elapsed="0.008754"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.914931" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:31:25.914931" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.915953" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:25.915953" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:31:25.914931" elapsed="0.001022"/>
</branch>
<status status="PASS" start="2025-06-19T13:31:25.914931" elapsed="0.001022"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-19T13:31:25.915953" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-19T13:31:25.915953" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:31:25.915953" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-19T13:31:26.124126" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-19T13:31:26.761961" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-19T13:31:26.777124" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-19T13:31:25.915953" elapsed="0.861171"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:26.777124" elapsed="0.001033"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-19T13:31:25.914931" elapsed="0.863226"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000020CFEDBB6B0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000020CFEDAA420&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-19T13:31:26.778157" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-19T13:31:26.778157" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T13:31:26.778157" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="2.964747"/>
</kw>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="2.964747"/>
</branch>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="2.964747"/>
</if>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="2.964747"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.742904" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.742904" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.743908" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.744907" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.744907" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.746368" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.747402" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.747402" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.748905" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.749477" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.749477" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.750456" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.751453" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T13:31:29.752319" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T13:31:29.742904" elapsed="0.009415"/>
</branch>
<status status="PASS" start="2025-06-19T13:31:26.778157" elapsed="2.974162"/>
</if>
<status status="PASS" start="2025-06-19T13:31:25.914931" elapsed="3.837388"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-19T13:31:29.752319" elapsed="0.068275"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-19T13:31:29.830407" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-19T13:31:29.830407" elapsed="5.872462"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:29.820594" elapsed="5.882711"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:31:45.704970" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:31:35.703463" elapsed="10.001507"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:31:45.704970" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:31:45.717374" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:31:45.704970" elapsed="0.012404"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:31:45.717374" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:31:55.717598" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:31:45.717374" elapsed="10.000224"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:31:55.717598" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:31:55.717598" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:31:55.717598" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:31:55.717598" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:32:00.731176" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:31:55.730606" elapsed="5.000570"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:00.731176" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:32:00.743028" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:00.731176" elapsed="0.011852"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T13:32:00.743028" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-19T13:31:29.752319" elapsed="30.990709"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:00.776900" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:00.743028" elapsed="0.033872"/>
</kw>
<msg time="2025-06-19T13:32:00.776900" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:00.743028" elapsed="0.033872"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:00.776900" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:00.776900" elapsed="0.112911"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:00.889811" elapsed="0.032852"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:00.922663" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:00.922663" elapsed="0.183810"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:01.107473" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:01.107473" elapsed="0.508790"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:32:03.618106" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:32:01.616263" elapsed="2.001843"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:03.638841" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:03.618106" elapsed="0.020735"/>
</kw>
<msg time="2025-06-19T13:32:03.638841" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:03.618106" elapsed="0.020735"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:00.776900" elapsed="2.861941"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:03.638841" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:03.638841" elapsed="0.102322"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:03.741163" elapsed="0.035213"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:03.776376" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:03.776376" elapsed="0.171556"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:03.947932" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:03.947932" elapsed="0.077377"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:32:06.040416" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:32:04.025309" elapsed="2.015107"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:06.984009" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-44.png"&gt;&lt;img src="selenium-screenshot-44.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-19T13:32:06.985009" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-19T13:32:06.042447" elapsed="0.960183">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-19T13:32:07.003641" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:06.041500" elapsed="0.962141"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:03.638841" elapsed="3.364800"/>
</iter>
<status status="PASS" start="2025-06-19T13:32:00.776900" elapsed="6.227741"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:07.004641" elapsed="0.001566"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:07.158753" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-45.png"&gt;&lt;img src="selenium-screenshot-45.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-19T13:32:07.006207" elapsed="0.152546"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-19T13:31:25.903977" elapsed="41.255781"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="41.262088"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-19T13:31:25.898719" elapsed="41.262088"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:07.162757" elapsed="0.100214"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:07.272912" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:07.272912" elapsed="4.056105"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:11.330050" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:07.161831" elapsed="4.168219"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:11.330050" elapsed="0.048369"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:11.378419" elapsed="0.020858"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.495822" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.399277" elapsed="0.096545"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:11.495822" elapsed="0.019529"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.572292" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.515351" elapsed="0.056941"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.650921" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.572292" elapsed="0.078629"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.714031" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.650921" elapsed="0.063110"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.794055" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.714031" elapsed="0.080024"/>
</kw>
<msg time="2025-06-19T13:32:11.794055" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:11.714031" elapsed="0.080024"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.856546" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.794055" elapsed="0.062491"/>
</kw>
<msg time="2025-06-19T13:32:11.856546" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:11.794055" elapsed="0.062491"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.923846" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.856546" elapsed="0.067300"/>
</kw>
<msg time="2025-06-19T13:32:11.923846" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T13:32:11.856546" elapsed="0.067300"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:11.923846" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:11.330050" elapsed="0.593796"/>
</kw>
<kw name="Then The user selects number of rows from the Rows per Page Menu" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="changeRows"]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:11.923846" elapsed="0.020344"/>
</kw>
<kw name="Select From List By Value" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:11.944190" level="INFO">Selecting options from selection list 'xpath=//*[@id="changeRows"]' by value 5.</msg>
<arg>xpath=//*[@id="changeRows"]</arg>
<arg>${NUM_ROWS}</arg>
<doc>Selects options from selection list ``locator`` by ``values``.</doc>
<status status="PASS" start="2025-06-19T13:32:11.944190" elapsed="0.092569"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Number of Rows Selected: ${NUM_ROWS}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:12.036759" elapsed="0.000998"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:32:12.038753" level="INFO">${Selected_rows_per_page} = 5</msg>
<var>${Selected_rows_per_page}</var>
<arg>${NUM_ROWS}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:32:12.037757" elapsed="0.000996"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:32:12.038753" level="INFO">${Global_Selected_rows_per_page} = 5</msg>
<arg>${Global_Selected_rows_per_page}</arg>
<arg>${Selected_rows_per_page}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:32:12.038753" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.039447" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:32:12.038753" elapsed="5.000694"/>
</kw>
<arg>${NUM_ROWS}</arg>
<status status="PASS" start="2025-06-19T13:32:11.923846" elapsed="5.115601"/>
</kw>
<kw name="And The user reads number of ATM rows on Frontend" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${Row_Count_Element}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:17.039447" elapsed="0.028299"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${text_row_count_element} = Showing 1 to 10 of 5808 rows</msg>
<var>${text_row_count_element}</var>
<arg>${Row_Count_Element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.067746" elapsed="0.015621"/>
</kw>
<kw name="Get Regexp Matches" owner="String">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${matches} = ['1', '10', '5808']</msg>
<var>${matches}</var>
<arg>${text_row_count_element}</arg>
<arg>${REGEX_PATTERN}</arg>
<doc>Returns a list of all non-overlapping matches in the given string.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${matches} == []</arg>
<arg>Fail</arg>
<arg>No matches found for pattern '${REGEX_PATTERN}'</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${frontend_row_count} = 5808</msg>
<var>${frontend_row_count}</var>
<arg>${matches}</arg>
<arg>2</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Selected_rows_per_page_high} = 10</msg>
<var>${Selected_rows_per_page_high}</var>
<arg>${matches}</arg>
<arg>1</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Selected_rows_per_page_low} = 1</msg>
<var>${Selected_rows_per_page_low}</var>
<arg>${matches}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${frontend_row_count} = 5808</msg>
<var>${frontend_row_count}</var>
<arg>${frontend_row_count}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Selected_rows_per_page_low} = 1</msg>
<var>${Selected_rows_per_page_low}</var>
<arg>${Selected_rows_per_page_low}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Selected_rows_per_page_high} = 10</msg>
<var>${Selected_rows_per_page_high}</var>
<arg>${Selected_rows_per_page_high}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Global_frontend_row_count} = 5808</msg>
<arg>${Global_frontend_row_count}</arg>
<arg>${frontend_row_count}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Global_Selected_rows_per_page_low} = 1</msg>
<arg>${Global_Selected_rows_per_page_low}</arg>
<arg>${Selected_rows_per_page_low}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Global_Selected_rows_per_page_high} = 10</msg>
<arg>${Global_Selected_rows_per_page_high}</arg>
<arg>${Selected_rows_per_page_high}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${number_of_rows_on_frontend} = 10</msg>
<var>${number_of_rows_on_frontend}</var>
<arg>(int(${Selected_rows_per_page_high} - ${Selected_rows_per_page_low} + 1))</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.083367" level="INFO">${Global_Calc_Number_of_Rows} = 10</msg>
<arg>${Global_Calc_Number_of_Rows}</arg>
<arg>${number_of_rows_on_frontend}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------${text_row_count_element}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Frontend Row Count: ${Global_frontend_row_count}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Lowest Row Number Shown on Page: ${Global_Selected_rows_per_page_low}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Highest Row Number Shown on Page: ${Global_Selected_rows_per_page_high}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Calculated Number of Rows: ${Global_Calc_Number_of_Rows}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:17.039447" elapsed="0.043920"/>
</kw>
<kw name="Then The user verifies the number of rows displayed is equal to the number of rows selected" owner="ATMDetails">
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:17.106730" level="INFO">${datarows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="9c23e378e6e3b80dd7b078a17b68d20f", element="f.021294932AFEC16EB0A7A64DF9927FAB.d.85B6E0D4417BF5899CE8D84A2B8251B6.e.205")&gt;, &lt;selenium.webdri...</msg>
<var>${datarows}</var>
<arg>${TABLE_XPATH}//tbody//tr</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.083367" elapsed="0.023363"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.106730" level="INFO">Length is 10.</msg>
<msg time="2025-06-19T13:32:17.106730" level="INFO">${row_count} = 10</msg>
<var>${row_count}</var>
<arg>${datarows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-19T13:32:17.106730" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-06-19T13:32:17.106730" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<msg time="2025-06-19T13:32:17.106730" level="FAIL">--------------------------Row counts do not match. Selected Rows Per Page: 5, Number of Rows Found on Table: 10: 10 != 5</msg>
<arg>${row_count}</arg>
<arg>${Global_Selected_rows_per_page}</arg>
<arg>--------------------------Row counts do not match. Selected Rows Per Page: ${Global_Selected_rows_per_page}, Number of Rows Found on Table: ${row_count}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="FAIL" start="2025-06-19T13:32:17.106730" elapsed="0.000000">--------------------------Row counts do not match. Selected Rows Per Page: 5, Number of Rows Found on Table: 10: 10 != 5</status>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${Global_Calc_Number_of_Rows}</arg>
<arg>${Global_Selected_rows_per_page}</arg>
<arg>--------------------------Row counts do not match. Selected Rows Per Page: ${Global_Selected_rows_per_page}, Calculate Number of rows on Frontend: ${Global_frontend_row_count}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-06-19T13:32:17.118750" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Row counts match. Test passed.</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-19T13:32:17.118750" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2025-06-19T13:32:17.083367" elapsed="0.035383">--------------------------Row counts do not match. Selected Rows Per Page: 5, Number of Rows Found on Table: 10: 10 != 5</status>
</kw>
<arg>Verify Sort Selection Functionality: Sort with Changed Rows per Page</arg>
<arg>VMS_UAT</arg>
<arg>5</arg>
<status status="FAIL" start="2025-06-19T13:31:25.898719" elapsed="51.220031">--------------------------Row counts do not match. Selected Rows Per Page: 5, Number of Rows Found on Table: 10: 10 != 5</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T13:32:17.120137" elapsed="0.022649"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T13:32:17.142786" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T13:32:17.142786" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T13:32:17.142786" elapsed="2.598757"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T13:32:22.742013" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T13:32:19.741543" elapsed="3.000470"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-19T13:32:22.742013" elapsed="3.794179"/>
</kw>
<status status="PASS" start="2025-06-19T13:32:17.119776" elapsed="9.416416"/>
</kw>
<doc>Verify Sort Selection Functionality: Sort with Changed Rows per Page</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-19T13:31:25.898719" elapsed="60.637473">--------------------------Row counts do not match. Selected Rows Per Page: 5, Number of Rows Found on Table: 10: 10 != 5</status>
</test>
<doc>Verify Sort Selection Functionality: Sort with Changed Rows per Page</doc>
<status status="FAIL" start="2025-06-19T13:31:23.182408" elapsed="63.355563"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">ATM DETAILS</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="0" fail="1" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-19T13:31:23.182408" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T13:31:25.593849" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.593849" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 258: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 298: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 343: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 398: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 694: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 716: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 747: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 783: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 793: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.609475" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 804: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.614965" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 822: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.614965" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 829: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.616065" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 849: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.616189" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 876: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.616714" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 907: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.616714" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1013: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.617743" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1241: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.619604" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1323: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.638081" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.639211" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.657033" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.660545" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 372: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T13:31:25.882709" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T13:31:25.890738" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-19T13:31:26.778157" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-19T13:31:26.778157" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T13:31:45.704970" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:31:55.717598" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T13:32:00.731176" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
